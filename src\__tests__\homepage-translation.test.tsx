/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { HomePageClient } from '@/components/home-page-client'

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: () => ({
    locale: 'en',
    locales: ['en', 'zh'],
    defaultLocale: 'en',
    push: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/'
  })
}))

// Mock the translation files
jest.mock('../../locales/en/homepage.json', () => ({
  default: {
    banner: {
      limitedOffer: "🎉 Limited Offer: New sign-up users receive 10 free credits for trial!"
    },
    hero: {
      title: {
        line1: "Exhausted with reading hundreds",
        line2: "of pages of"
      },
      documentTypes: [
        "financial reports",
        "legal documents", 
        "insurance policies",
        "request for proposals",
        "research papers",
        "compliance documents"
      ],
      subtitle: "DocuChampAI is the AI-powered document analysis platform for professionals who value efficiency over manual processes.",
      description: "Transform your document workflow with advanced AI analysis. Extract insights from PDFs, analyze charts & tables, and automate reports - wherever you are.",
      buttons: {
        getStartedFree: "Get Started for Free",
        viewPricing: "View Pricing Plans"
      }
    },
    videoSection: {
      title: "See DocuChampAI in Action",
      description: "Watch how our AI-powered document analysis transforms complex documents into actionable insights in minutes.",
      loadingText: "Loading video..."
    },
    whySection: {
      title: "Why DocuChampAI?",
      description: "See how our AI-powered document analysis platform outperforms traditional automation tools"
    },
    aiFeatures: {
      title: "AI That \"Sees\" Pictures, Charts, and Anything a Human Can",
      description: "DocuChampAI doesn't just scan, it truly seeing pictures and patterns like a person would."
    },
    cta: {
      title: "Ready to Transform Your Document Analysis?",
      description: "Join thousands of users who are already using AI to analyze their documents smarter.",
      button: "Get Started Today"
    }
  }
}), { virtual: true })

// Mock other components to focus on translation testing
jest.mock('@/components/home/<USER>', () => ({
  HomeTrialSection: () => <div data-testid="trial-section">Trial Section</div>
}))

jest.mock('@/components/home/<USER>', () => ({
  IntroVideoSection: () => <div data-testid="video-section">Video Section</div>
}))

jest.mock('@/components/social/social-share', () => ({
  FloatingSocialShare: () => <div data-testid="social-share">Social Share</div>
}))

jest.mock('@/components/auth/auth-modal', () => ({
  AuthModal: () => <div data-testid="auth-modal">Auth Modal</div>
}))

describe('Homepage Translation', () => {
  it('should render English translations correctly', () => {
    render(<HomePageClient />)
    
    // Check banner translation
    expect(screen.getByText('🎉 Limited Offer: New sign-up users receive 10 free credits for trial!')).toBeInTheDocument()
    
    // Check hero section translations
    expect(screen.getByText('Exhausted with reading hundreds')).toBeInTheDocument()
    expect(screen.getByText('Get Started for Free')).toBeInTheDocument()
    expect(screen.getByText('View Pricing Plans')).toBeInTheDocument()
    
    // Check subtitle and description
    expect(screen.getByText(/DocuChampAI is the AI-powered document analysis platform/)).toBeInTheDocument()
    expect(screen.getByText(/Transform your document workflow with advanced AI analysis/)).toBeInTheDocument()
    
    // Check section titles
    expect(screen.getByText('Why DocuChampAI?')).toBeInTheDocument()
    expect(screen.getByText('Ready to Transform Your Document Analysis?')).toBeInTheDocument()
    expect(screen.getByText('Get Started Today')).toBeInTheDocument()
  })

  it('should render dynamic document types', () => {
    render(<HomePageClient />)
    
    // The dynamic component should show one of the document types
    // We can't predict which one due to the rotation, but we can check that the component renders
    const heroSection = screen.getByText('Exhausted with reading hundreds').closest('section')
    expect(heroSection).toBeInTheDocument()
  })

  it('should have proper button functionality', () => {
    render(<HomePageClient />)
    
    const getStartedButton = screen.getByText('Get Started for Free')
    const viewPricingButton = screen.getByText('View Pricing Plans')
    
    expect(getStartedButton).toBeInTheDocument()
    expect(viewPricingButton).toBeInTheDocument()
    
    // Check that buttons are clickable
    expect(getStartedButton.closest('button')).toBeInTheDocument()
    expect(viewPricingButton.closest('a')).toHaveAttribute('href', '/pricing')
  })

  it('should render all major sections', () => {
    render(<HomePageClient />)
    
    // Check that all major sections are present
    expect(screen.getByTestId('video-section')).toBeInTheDocument()
    expect(screen.getByTestId('trial-section')).toBeInTheDocument()
    expect(screen.getByTestId('social-share')).toBeInTheDocument()
  })

  it('should have proper semantic structure', () => {
    render(<HomePageClient />)
    
    // Check for proper heading hierarchy
    const h1 = screen.getByRole('heading', { level: 1 })
    expect(h1).toBeInTheDocument()
    
    const h2Elements = screen.getAllByRole('heading', { level: 2 })
    expect(h2Elements.length).toBeGreaterThan(0)
    
    // Check for main landmark
    const main = screen.getByRole('main')
    expect(main).toBeInTheDocument()
  })
})
