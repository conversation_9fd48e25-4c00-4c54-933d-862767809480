'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Play } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TrialUploadZone } from './trial-analysis/trial-upload-zone'
import { TrialSettings } from './trial-analysis/trial-settings'
import { TrialProgress } from './trial-analysis/trial-progress'
import { TrialResults } from './trial-analysis/trial-results'
import { AuthModal } from '@/components/auth/auth-modal'
import {
  getTrialUsageInfo,
  recordTrialUsage,
  validateTrialUsageEnhanced
} from '@/lib/trial-usage-tracker'
import { createAnalysisFormData } from '@/lib/document-utils'
import { createAnalysisSSEManager, type AnalysisSSEManager } from '@/lib/analysis-sse'
import { useTranslation } from '@/hooks/use-translation'

export function HomeTrialSection() {
  const { t } = useTranslation('trial')

  // File and analysis state
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [language, setLanguage] = useState('English')
  const [reportLength, setReportLength] = useState('medium')
  const [isProcessing, setIsProcessing] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [currentOperationId, setCurrentOperationId] = useState<string | null>(null)
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null)
  const [isProgressTimerActive, setIsProgressTimerActive] = useState(false)

  // Ref for scrolling to results
  const resultsRef = useRef<HTMLDivElement>(null)

  // SSE Manager
  const sseManagerRef = useRef<AnalysisSSEManager | null>(null)

  // Trial usage state
  const [trialsRemaining, setTrialsRemaining] = useState(3)
  const [canUseTrial, setCanUseTrial] = useState(true)
  
  // Auth modal state
  const [authModal, setAuthModal] = useState({
    isOpen: false,
    mode: 'signup' as 'signin' | 'signup'
  })

  // Load trial usage info on mount with enhanced validation
  useEffect(() => {
    const loadTrialInfo = async () => {
      try {
        // Use enhanced validation that combines client and server-side checks
        const enhancedInfo = await validateTrialUsageEnhanced()
        setTrialsRemaining(enhancedInfo.remainingAttempts)
        setCanUseTrial(enhancedInfo.canUseTrial)
      } catch (error) {
        console.error('Failed to load enhanced trial info, falling back to client-side:', error)
        // Fallback to client-side only
        const usageInfo = getTrialUsageInfo()
        setTrialsRemaining(usageInfo.attemptsRemaining)
        setCanUseTrial(usageInfo.canUseTrialNow)
      }
    }

    loadTrialInfo()
  }, [])

  // Clean up progress timer and SSE connection on unmount
  useEffect(() => {
    return () => {
      if (progressTimer) {
        clearInterval(progressTimer)
      }
      if (sseManagerRef.current) {
        sseManagerRef.current.disconnect()
      }
    }
  }, [progressTimer])

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    setUploadedFile(file)
    setAnalysisResult(null)
    setErrorMessage(null)
  }, [])

  // Handle file removal
  const handleFileRemove = useCallback(() => {
    setUploadedFile(null)
    setAnalysisResult(null)
    setErrorMessage(null)
  }, [])

  // Start progress timer based on file size
  const startProgressTimer = useCallback((fileSize: number) => {
    // Calculate estimated time based on file size (5MB = 2 minutes)
    const estimatedTimeMs = Math.min(120000, Math.max(30000, fileSize / 5000))
    const intervalMs = 1000
    const incrementPerInterval = 100 / (estimatedTimeMs / intervalMs)
    
    let progress = 0
    
    // Clear any existing timer
    if (progressTimer) {
      clearInterval(progressTimer)
    }
    
    // Create new timer
    const timer = setInterval(() => {
      progress += incrementPerInterval
      
      // Cap at 99% until we get the webhook callback
      if (progress >= 99) {
        progress = 99
        clearInterval(timer)
      }
      
      setAnalysisProgress(progress)
    }, intervalMs)
    
    setProgressTimer(timer)
    
    return timer
  }, [progressTimer])

  // Calculate estimated time for display
  const calculateEstimatedTime = (fileSize: number): number => {
    // 5MB = 2 minutes, minimum 30 seconds
    return Math.min(120, Math.max(30, fileSize / 5000 / 1000))
  }

  // Format estimated time for display
  const formatEstimatedTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`
    }
    return `${Math.floor(seconds / 60)}:${String(Math.round(seconds % 60)).padStart(2, '0')} minutes`
  }

  // Handle analysis start
  const handleAnalysis = async () => {
    // Clear previous state
    setErrorMessage(null)
    setAnalysisResult(null)
    setCurrentOperationId(null)

    // Check if user can use trial
    if (!canUseTrial) {
      setErrorMessage(t('messages.trialsExhausted'))
      return
    }

    // Check if file is uploaded
    if (!uploadedFile) {
      setErrorMessage(t('messages.uploadFirst'))
      return
    }

    // Start processing
    setIsProcessing(true)
    setAnalysisProgress(0)
    
    // Start progress timer based on file size
    if (uploadedFile) {
      startProgressTimer(uploadedFile.size)
    }

    try {
      // Record trial usage (now async with server-side tracking)
      await recordTrialUsage()

      // Re-validate trial status after recording usage
      const updatedInfo = await validateTrialUsageEnhanced()
      setTrialsRemaining(updatedInfo.remainingAttempts)
      setCanUseTrial(updatedInfo.canUseTrial)

      // Create form data for file upload
      const formData = createAnalysisFormData({
        file: uploadedFile,
        analysisType: 'office', // Fixed for trial
        sections: undefined, // Let backend use default sections
        language: language,
        reportLength: reportLength as 'short' | 'medium' | 'long'
      })

      // Add trial flag
      formData.append('isTrial', 'true')

      // Start analysis using API
      const response = await fetch('/api/analyze', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start analysis')
      }

      const data = await response.json()
      const operationId = data.operationId
      setCurrentOperationId(operationId)

      // Initialize SSE manager if not already created
      if (!sseManagerRef.current) {
        sseManagerRef.current = createAnalysisSSEManager({
          // Callbacks for state management
          setIsProcessing,
          setAnalysisProgress,
          setAnalysisResult,
          setErrorMessage,
          setCurrentOperationId,

          // Helper functions
          clearProgressTimer: () => {
            if (progressTimer) {
              clearInterval(progressTimer)
              setProgressTimer(null)
              setIsProgressTimerActive(false)
            }
          },

          // Additional data
          fileName: uploadedFile?.name,

          // Scroll to results function - scroll to top of results section
          scrollToResults: () => {
            if (resultsRef.current) {
              resultsRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              })
            }
          }
        })
      }

      // Connect to SSE stream
      sseManagerRef.current.connect(operationId)

    } catch (error: any) {
      console.error('Analysis error:', error)
      setErrorMessage(error.message || 'An error occurred during analysis')
      setIsProcessing(false)
      
      // Clear progress timer
      if (progressTimer) {
        clearInterval(progressTimer)
      }
    }
  }

  // Handle sign up click
  const handleSignUpClick = useCallback(() => {
    setAuthModal({ isOpen: true, mode: 'signup' })
  }, [])

  return (
    <section className="py-16 bg-gray-50" id="try-it-free">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            {t('section.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('section.description')}
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* Trial Content */}
          <div className="p-6 md:p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Settings */}
              <div className="space-y-6">
                <TrialSettings
                  language={language}
                  reportLength={reportLength}
                  onLanguageChange={setLanguage}
                  onReportLengthChange={setReportLength}
                  onSignUpClick={handleSignUpClick}
                />
              </div>

              {/* Right Column - Upload & Results */}
              <div className="space-y-6">
                {/* Add top margin to align with left column */}
                <div className="mt-8">
                  {(!isProcessing) || errorMessage ? (
                    <TrialUploadZone
                      onFileSelect={handleFileSelect}
                      onFileRemove={handleFileRemove}
                      selectedFile={uploadedFile}
                      disabled={isProcessing || !canUseTrial}
                      trialsRemaining={trialsRemaining}
                      canUseTrial={canUseTrial}
                    />
                  ) : null}
                </div>

                {isProcessing && (
                  <TrialProgress
                    status="processing"
                    progress={analysisProgress}
                    fileName={uploadedFile?.name}
                    estimatedTime={uploadedFile ? formatEstimatedTime(calculateEstimatedTime(uploadedFile.size)) : undefined}
                  />
                )}



                {/* Error Message */}
                {errorMessage && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
                    <p className="font-medium">{errorMessage}</p>
                    <button
                      onClick={() => {
                        setErrorMessage(null)
                        setAnalysisResult(null)
                        setAnalysisProgress(0)
                        setCurrentOperationId(null)
                        if (progressTimer) {
                          clearInterval(progressTimer)
                          setProgressTimer(null)
                          setIsProgressTimerActive(false)
                        }
                        if (sseManagerRef.current) {
                          sseManagerRef.current.disconnect()
                        }
                      }}
                      className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                    >
                      {t('buttons.tryAgain')}
                    </button>
                  </div>
                )}

                {/* Start Analysis Button */}
                {!isProcessing && !analysisResult && uploadedFile && canUseTrial && (
                  <div className="mt-6">
                    <Button
                      onClick={handleAnalysis}
                      size="lg"
                      className="w-full bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-medium"
                    >
                      <Play className="w-5 h-5 mr-3" />
                      {t('buttons.startAnalysis')}
                    </Button>
                  </div>
                )}

                {/* Sign Up Button (when trials exhausted) */}
                {!isProcessing && !analysisResult && !canUseTrial && (
                  <div className="mt-6">
                    <Button
                      onClick={handleSignUpClick}
                      size="lg"
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 text-lg font-medium"
                    >
                      {t('buttons.signUpFree')}
                    </Button>
                    <p className="text-center text-sm text-gray-500 mt-2">
                      {t('messages.signUpCredits')}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Full-width Results Section */}
        {analysisResult && !isProcessing && (
          <div ref={resultsRef} className="mt-8 w-full">
            <TrialResults
              analysisResult={analysisResult}
              onSignUpClick={handleSignUpClick}
              trialsRemaining={trialsRemaining}
            />
          </div>
        )}
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={() => setAuthModal({ ...authModal, isOpen: false })}
        initialMode={authModal.mode}
      />
    </section>
  )
}
