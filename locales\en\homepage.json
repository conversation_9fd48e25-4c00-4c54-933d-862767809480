{"banner": {"limitedOffer": "🎉 Limited Offer: New sign-up users receive 10 free credits for trial!"}, "hero": {"title": {"line1": "Overwhelmed by the Mountain of", "line2": ""}, "documentTypes": ["Financial Reports", "Legal Documents", "Insurance Policies", "Request for Proposals", "Research Papers", "Compliance Documents"], "subtitle": "DocuChampAI is designed for busy people like you, making paperwork seamless, accurate and smart.", "description": "More Than Text: Our AI understands every part of your documents—images, digrams, complex tables, charts, and even handwriting.", "buttons": {"getStartedFree": "Get Started for Free", "viewPricing": "View Subscription Plans"}}, "videoSection": {"title": "See DocuChampAI in Action", "description": "Watch how our AI-powered document analysis transforms complex documents into actionable insights in minutes.", "loadingText": "Loading video...", "videoDescription": "This demonstration shows the complete workflow from document upload to AI analysis, highlighting key features like multimodal AI extraction, customizable templates, and automated report generation.", "muteButton": "Toggle sound"}, "valueProposition": {"heavyLifting": "Let AI handle the heavy lifting while you focus on what matters most.", "startSaving": "Start Saving Time Today"}, "trialSection": {"title": "Try DocuChampAI - Analyze your Document Instantly", "description": "Experience the power of AI document analysis without signing up. Upload a document and get instant insights, summaries, and recommendations."}, "whySection": {"title": "Why DocuChampAI?", "description": "See how our AI-powered document analysis platform outperforms traditional automation tools", "comparisonTable": {"headers": {"feature": "Feature", "others": "Others", "docuchamp": "DocuChampAI"}, "rows": {"dataUnderstanding": {"feature": "Data Understanding", "others": "Text only, many data loss", "docuchamp": "DocuChampAI reads everything, image, chart, table, text or even handwriting"}, "documentSize": {"feature": "Document Size", "others": "Small documents, <50 pages", "docuchamp": "Support files up to 300 pages, enabling deep insights from full-length materials"}, "useCase": {"feature": "Use Case", "others": "Chatbot interface, not practical for daily work, too many manual copy-and-paste", "docuchamp": "Designed for efficiency, output to .docx, .pdf directly without copy-and-paste"}}}}, "documentCarousel": {"title": "Streamlining Your Paperwork", "description": "Let our AI lighten your load—summarizing and extracting key points from insurance forms, receipts, contracts, and more, so you can breathe a little easier."}, "carousel": {"categories": {"contract": "Contract", "financial": "Financial", "generic": "Business", "handwritten": "Handwritten", "insurance": "Insurance", "manufacturing": "Manufacturing"}}, "documentNames": {"NDA": "NDA", "Employee": "Employee Contract", "Financial Report": "Financial Report", "Financial Statement": "Financial Statement", "Invoice": "Invoice", "Purchase Order": "Purchase Order", "RFP": "RFP", "Flowchart": "Handwritten Flowchart", "Form": "Handwritten Form", "Claim Form": "Claim Form", "Policy": "Insurance Policy", "Specification": "Manufacturing Spec"}, "aiFeatures": {"title": "AI That \"Sees\" Pictures, Charts, and Anything a Human Can", "description": "DocuChampAI doesn't just scan, it truly seeing pictures and patterns like a person would. Whether it's numbers, charts, tables or photos, our AI gives every element within your documents the attention.", "features": {"textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}, "chartRecognition": {"title": "Chart Recognition", "description": "Identify and interpret charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageAnalysis": {"title": "Image Analysis", "description": "Analyze images, diagrams, and visual elements within documents"}}, "supportInfo": {"pages": "📄 Supports up to 300 pages", "formats": "📁 PDF, DOCX, PPTX formats"}, "featureList": {"chartAnalysis": {"title": "Chart Analysis", "description": "Understand financial charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageRecognition": {"title": "Image Recognition", "description": "Understand diagrams, photos, and visual elements within documents"}, "textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}}}, "cta": {"title": "Ready to Transform Your Document Analysis?", "description": "Join thousands of users who are already using AI to analyze their documents smarter.", "button": "Get Started Today"}, "heroFeatures": {"title": "What Makes DocuChampAI Unique?", "scrollForMore": "Scroll for more", "wantMoreFeatures": "Want more features? Let us know!", "contactUs": "Contact Us", "list": [{"title": "Visual Data Interpretation", "description": "Comprehensively analyze diagrams, flowcharts, screenshots, timelines, charts, and other visuals to reveal underlying insights and connections."}, {"title": "Handwriting Recognition", "description": "Seamlessly convert handwritten notes, diagrams, and checkboxes into accurate digital text."}, {"title": "Excel Data Analysis", "description": "Transform raw Excel data and complex tables into professional reports with visualizations, key metrics, and contextual highlights."}, {"title": "Multilingual Translation", "description": "Effortlessly translate documents into any required language, maintaining accuracy and context."}, {"title": "Form Validation", "description": "Ensure all required fields are complete, and instantly flag missing or incorrect information."}, {"title": "Discrepancy Detection", "description": "Compare multiple document versions to quickly identify and highlight inconsistencies or errors."}, {"title": "Intelligent Data Entry", "description": "Automatically extract relevant information from physical or scanned documents for accurate system integration."}, {"title": "Key Information Extraction", "description": "Efficiently pinpoint and highlight critical entities such as individuals, organizations, and significant dates within documents."}, {"title": "Contract Analysis", "description": "Identify and summarize essential contract elements such as expiration dates, payment terms, and penalty clauses for informed decision-making."}]}, "scrollFeatures": {"list": [{"features": ["Tailored for different document types", "Accurate and comprehensive analysis", "Perfect starting point"]}, {"features": ["Intelligent section detection", "Custom section selection", "Focus on relevant content"]}, {"features": ["200+ Multiple language support", "Accurate translations by GenAI", "Localized output format"]}, {"features": ["Secure large file upload", "Support multiple file formats", "Up to 300 pages"]}]}, "features": {"title": "Select Your Analysis Template", "description": "Choose from our professionally designed templates tailored for different document types and analysis needs. Whether you're analyzing financial reports, legal documents, or general business documents, our templates provide the perfect starting point for accurate and comprehensive analysis.", "scrollForMore": "Scroll for more", "wantMoreFeatures": "Want more features? Let us know!", "contactUs": "Contact Us", "videoToggle": {"preview": "Analysis Results Preview", "docx": "DOCX Document"}, "list": [{"title": "Select Your Analysis Template", "description": "Choose from our professionally designed templates tailored for different document types and analysis needs. Whether you're analyzing financial reports, legal documents, or general business documents, our templates provide the perfect starting point for accurate and comprehensive analysis.", "features": ["Finance, Legal, Construction, Manufacturing and General templates", "Pre-configured analysis parameters", "Industry-specific insights"]}, {"title": "Choose Document Sections", "description": "Pick the specific sections of your document that you want to analyze for targeted insights. Our intelligent section detection helps you focus on the most relevant parts of your document, ensuring you get precise analysis results that matter most to your workflow.", "features": ["Intelligent section detection", "Custom section selection", "Focus on relevant content"]}], "steps": {"step1": {"title": "Select Your Analysis Template", "description": "Choose from our professionally designed templates tailored for different document types and analysis needs. Whether you're analyzing financial reports, legal documents, or general business documents, our templates provide the perfect starting point for accurate and comprehensive analysis."}, "step2": {"title": "Choose Document Sections", "description": "Pick the specific sections of your document that you want to analyze for targeted insights. Our intelligent section detection helps you focus on the most relevant parts of your document, ensuring you get precise analysis results that matter most to your workflow."}, "step3": {"title": "Select Language", "description": "Choose the language you want your analysis results in. We support multiple languages to ensure you receive analysis results in the language you're most comfortable with."}, "step4": {"title": "Upload Document", "description": "Upload the document you need analyzed. We support various formats including PDF, DOCX, PPTX, and can handle documents up to 300 pages."}, "step5": {"title": "Get Analysis Results", "description": "Receive detailed analysis results within minutes. Our AI provides structured summaries, key insights, and downloadable reports.", "features": ["Comprehensive summary", "Native output to Microsoft Word", "Export options"]}}}}