'use client'

import { useTranslation } from '@/hooks/use-translation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function FeaturesList() {
  const { t } = useTranslation('homepage')

  // Get the features list from translations
  const featuresList = t('heroFeatures.list') as Array<{title: string, description: string}> || []

  return (
    <div className="bg-white/90 backdrop-blur-md rounded-3xl p-6 shadow-xl border border-white/30">
      <h3 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-5">
        {t('heroFeatures.title')}
      </h3>
      
      {/* Scrollable Container - Fixed Height */}
      <div className="h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 scrollbar-track-gray-100 pr-2">
        <div className="space-y-4">
          {featuresList.map((feature, index) => (
            <div
              key={index}
              className="rounded-xl p-4 bg-gray-50/80 border border-gray-200/50 hover:bg-gray-100/80 transition-colors"
            >
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                <h4 className="font-medium text-gray-800 text-sm">{feature.title}</h4>
              </div>
              <p className="text-xs text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="mt-3 text-center">
        <div className="inline-flex items-center space-x-1 text-xs text-gray-500">
          <span>{t('heroFeatures.scrollForMore')}</span>
          <svg className="w-3 h-3 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Contact Section */}
      <div className="mt-6 pt-4 border-t border-gray-200/50 text-center">
        <p className="text-sm text-gray-600 mb-3">
          {t('heroFeatures.wantMoreFeatures')}
        </p>
        <Link href="/contact" aria-label="Contact us for more features">
          <Button
            variant="outline"
            size="sm"
            className="border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200"
          >
            {t('heroFeatures.contactUs')}
          </Button>
        </Link>
      </div>
    </div>
  )
}
