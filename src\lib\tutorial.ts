import { TutorialStep } from '@/components/onboarding/tutorial-overlay'

// Tutorial state management
const TUTORIAL_STORAGE_KEY = 'docuchampai_tutorial_state'

export interface TutorialState {
  hasCompletedOnboarding: boolean
  hasSeenWelcomeMessage: boolean
  completedTutorials: string[]
  lastTutorialDate?: string
}

export function getTutorialState(): TutorialState {
  if (typeof window === 'undefined') {
    return {
      hasCompletedOnboarding: false,
      hasSeenWelcomeMessage: false,
      completedTutorials: []
    }
  }

  try {
    const saved = localStorage.getItem(TUTORIAL_STORAGE_KEY)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load tutorial state:', error)
  }

  return {
    hasCompletedOnboarding: false,
    hasSeenWelcomeMessage: false,
    completedTutorials: []
  }
}

export function saveTutorialState(state: TutorialState): void {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem(TUTORIAL_STORAGE_KEY, JSON.stringify(state))
  } catch (error) {
    console.error('Failed to save tutorial state:', error)
  }
}

export function markTutorialCompleted(tutorialId: string): void {
  const state = getTutorialState()
  if (!state.completedTutorials.includes(tutorialId)) {
    state.completedTutorials.push(tutorialId)
    state.lastTutorialDate = new Date().toISOString()
    saveTutorialState(state)
  }
}

export function markOnboardingCompleted(): void {
  const state = getTutorialState()
  state.hasCompletedOnboarding = true
  state.lastTutorialDate = new Date().toISOString()
  saveTutorialState(state)
}

export function markWelcomeMessageSeen(): void {
  const state = getTutorialState()
  state.hasSeenWelcomeMessage = true
  saveTutorialState(state)
}

export function shouldShowOnboarding(): boolean {
  const state = getTutorialState()
  return !state.hasCompletedOnboarding
}

export function shouldShowWelcomeMessage(): boolean {
  const state = getTutorialState()
  return !state.hasSeenWelcomeMessage
}

export function hasTutorialBeenCompleted(tutorialId: string): boolean {
  const state = getTutorialState()
  return state.completedTutorials.includes(tutorialId)
}

// Document Analysis Tutorial Steps
export const documentAnalysisTutorialSteps: TutorialStep[] = [
  {
    id: 'select-analysis-type',
    title: 'Step 1: Select Analysis Type',
    content: 'First, choose how you want to analyze your document. Different analysis types extract different insights from your documents.',
    targetSelector: '[data-tutorial="analysis-type-selector"]',
    position: 'bottom',
    highlightPadding: 12
  },
  {
    id: 'configure-sections',
    title: 'Step 2: Configure Analysis Sections',
    content: 'After selecting an analysis type, configure which sections you want to extract. This helps focus the AI on what matters most to you.',
    targetSelector: '[data-tutorial="sections-manager"]',
    position: 'bottom',
    highlightPadding: 12
  },
  {
    id: 'report-settings',
    title: 'Step 3: Report Settings',
    content: 'Choose your preferred language and report length. You can select from preset languages or specify a custom one.',
    targetSelector: '[data-tutorial="report-settings"]',
    position: 'bottom',
    highlightPadding: 12
  },
  {
    id: 'upload-document',
    title: 'Step 4: Upload Document',
    content: 'Upload your document here. We support PDF, DOC, DOCX, PPTX, and TXT files up to 50MB.',
    targetSelector: '[data-tutorial="file-upload"]',
    position: 'top',
    highlightPadding: 12
  },
  {
    id: 'start-analysis',
    title: 'Step 5: Start Analysis',
    content: 'Once everything is configured and your document is uploaded, click this button to start the analysis. It costs 1 credit per analysis.',
    targetSelector: '[data-tutorial="start-analysis"]',
    position: 'top',
    highlightPadding: 12
  },
  {
    id: 'tutorial-complete',
    title: 'Tutorial Complete! ✨',
    content: 'You\'re all set! You can restart this tutorial anytime by clicking the info button next to "Document Analysis". Happy analyzing!',
    targetSelector: 'body',
    position: 'center'
  }
]

// Check if user is new (just signed up)
export function isNewUser(userCredits: number, hasTransactions: boolean): boolean {
  // User is considered new if they have exactly 10 credits and no transaction history
  return userCredits === 10 && !hasTransactions
}

// Reset tutorial state (for testing or user request)
export function resetTutorialState(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TUTORIAL_STORAGE_KEY)
  }
}
