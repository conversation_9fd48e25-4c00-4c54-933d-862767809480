/**
 * @jest-environment jsdom
 */

import { renderHook } from '@testing-library/react'
import { useRouter } from 'next/router'
import { useTranslation, preloadTranslations, getAvailableLocales, isSupportedLocale } from '@/hooks/use-translation'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

// Mock translation files
jest.mock('../../../locales/en/common.json', () => ({
  default: {
    navigation: {
      home: 'Home',
      features: 'Features'
    },
    buttons: {
      getStarted: 'Get Started',
      cancel: 'Cancel'
    },
    forms: {
      email: 'Email',
      invalidEmail: 'Please enter a valid email address'
    },
    status: {
      loading: 'Loading...',
      error: 'Error'
    }
  }
}), { virtual: true })

jest.mock('../../../locales/zh/common.json', () => ({
  default: {
    navigation: {
      home: '首頁',
      features: '功能'
    },
    buttons: {
      getStarted: '開始使用',
      cancel: '取消'
    },
    forms: {
      email: '電子郵件',
      invalidEmail: '請輸入有效的電子郵件地址'
    },
    status: {
      loading: '載入中...',
      error: '錯誤'
    }
  }
}), { virtual: true })

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

describe('useTranslation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('English locale', () => {
    beforeEach(() => {
      mockUseRouter.mockReturnValue({
        locale: 'en',
        locales: ['en', 'zh'],
        defaultLocale: 'en'
      } as any)
    })

    it('should return English translations', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      // Wait for translations to load
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.locale).toBe('en')
      expect(result.current.t('navigation.home')).toBe('Home')
      expect(result.current.t('buttons.getStarted')).toBe('Get Started')
    })

    it('should handle nested translation keys', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.t('navigation.features')).toBe('Features')
      expect(result.current.t('forms.invalidEmail')).toBe('Please enter a valid email address')
    })

    it('should return key if translation not found', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.t('nonexistent.key')).toBe('nonexistent.key')
    })

    it('should check if translation exists', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.exists('navigation.home')).toBe(true)
      expect(result.current.exists('nonexistent.key')).toBe(false)
    })
  })

  describe('Chinese locale', () => {
    beforeEach(() => {
      mockUseRouter.mockReturnValue({
        locale: 'zh',
        locales: ['en', 'zh'],
        defaultLocale: 'en'
      } as any)
    })

    it('should return Chinese translations', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.locale).toBe('zh')
      expect(result.current.t('navigation.home')).toBe('首頁')
      expect(result.current.t('buttons.getStarted')).toBe('開始使用')
    })

    it('should handle Chinese nested translations', async () => {
      const { result } = renderHook(() => useTranslation('common'))
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(result.current.t('forms.email')).toBe('電子郵件')
      expect(result.current.t('status.loading')).toBe('載入中...')
    })
  })

  describe('Parameter interpolation', () => {
    beforeEach(() => {
      mockUseRouter.mockReturnValue({
        locale: 'en',
        locales: ['en', 'zh'],
        defaultLocale: 'en'
      } as any)
    })

    it('should interpolate parameters in translations', async () => {
      // Mock a translation with parameters
      jest.doMock('../../../locales/en/common.json', () => ({
        default: {
          messages: {
            welcome: 'Welcome {{name}}!',
            credits: 'You have {{count}} credits remaining'
          }
        }
      }), { virtual: true })

      const { result } = renderHook(() => useTranslation('common'))

      await new Promise(resolve => setTimeout(resolve, 100))

      expect(result.current.t('messages.welcome', { name: 'John' })).toBe('Welcome John!')
      expect(result.current.t('messages.credits', { count: 5 })).toBe('You have 5 credits remaining')
    })
  })

  describe('Fallback behavior', () => {
    beforeEach(() => {
      mockUseRouter.mockReturnValue({
        locale: 'en',
        locales: ['en', 'zh'],
        defaultLocale: 'en'
      } as any)
    })

    it('should fallback to English when locale is undefined', async () => {
      mockUseRouter.mockReturnValue({
        locale: undefined,
        locales: ['en', 'zh'],
        defaultLocale: 'en'
      } as any)

      const { result } = renderHook(() => useTranslation('common'))
      
      expect(result.current.locale).toBe('en')
    })
  })
})

describe('Utility functions', () => {
  describe('getAvailableLocales', () => {
    it('should return supported locales', () => {
      const locales = getAvailableLocales()
      expect(locales).toEqual(['en', 'zh'])
    })
  })

  describe('isSupportedLocale', () => {
    it('should validate supported locales', () => {
      expect(isSupportedLocale('en')).toBe(true)
      expect(isSupportedLocale('zh')).toBe(true)
      expect(isSupportedLocale('fr')).toBe(false)
      expect(isSupportedLocale('invalid')).toBe(false)
    })
  })

  describe('preloadTranslations', () => {
    it('should preload translations without errors', async () => {
      await expect(preloadTranslations('en', ['common'])).resolves.not.toThrow()
      await expect(preloadTranslations('zh', ['common'])).resolves.not.toThrow()
    })
  })
})
