{"pageTitle": "Document Analysis", "pageDescription": "Choose your analysis type and configure sections to extract targeted insights from your documents.", "historyButton": "History", "sections": {"selectAnalysisType": {"title": "1. Select Analysis Type", "placeholder": "Choose how you want to analyze your document", "useCases": {"finance": {"name": "Finance Report Analysis", "description": "Analyze financial reports and assess business performance", "analysisFocus": ["Financial data extraction", "Trend analysis", "Key metric calculation", "Risk indicator identification", "Performance summarization"]}, "office": {"name": "Office Document Analysis", "description": "General analysis for office documents and reports", "analysisFocus": ["Content structure analysis", "Information completeness", "Consistency checking", "Quality assessment", "Actionable insights extraction"]}, "legal": {"name": "Legal Document Validation", "description": "Validate legal documents for compliance and consistency", "analysisFocus": ["Clause consistency analysis", "Legal compliance checking", "Term definition validation", "Cross-reference verification", "Risk assessment"]}, "garment-bom": {"name": "Garment BOM Factory Readiness", "description": "Analyze garment bills of materials and assess factory readiness", "analysisFocus": ["BOM component extraction", "Sketch annotation analysis", "Client communication detection", "Cross-reference discrepancy detection", "Factory readiness assessment"]}, "education": {"name": "Education Content Validation", "description": "Validate educational content and assess learning objectives", "analysisFocus": ["Content accuracy verification", "Learning objective alignment", "Assessment criteria checking", "Educational standard compliance", "Student progress evaluation"]}, "custom": {"name": "Custom Analysis", "description": "Create your own custom analysis template", "analysisFocus": ["Custom analysis focus 1", "Custom analysis focus 2", "Custom analysis focus 3"]}}, "moreText": "more"}, "configureAnalysisSections": {"title": "2. Configure Analysis Sections", "selectAnalysisType": "Select an analysis type", "chooseAnalysisType": "Choose an analysis type above to configure sections", "analysisSections": {"title": "Analysis Sections", "addButton": "Add", "placeholder": "Enter section name...", "sections": {"documentinformation": "Document Information", "executivesummary": "Executive Summary", "financialoverview": "Financial Overview", "keymetricsanalysis": "Key Metrics Analysis", "trendanalysis": "Trend Analysis", "issuesrisksfound": "Issues & Risks Found", "financialhealthassessment": "Financial Health Assessment", "strategicrecommendations": "Strategic Recommendations", "actionplan": "Action Plan", "marketcomparison": "Market Comparison", "cashflowforecasting": "Cash Flow Forecasting", "investmentanalysisroi": "Investment Analysis & ROI", "debtmanagementassessment": "Debt Management Assessment", "taximplicationsoptimization": "Tax Implications & Optimization", "auditreadinessassessment": "Audit Readiness Assessment", "contentoverview": "Content Overview", "structureanalysis": "Structure Analysis", "keyinformationextracted": "Key Information Extracted", "qualityassessment": "Quality Assessment", "issuesfound": "Issues Found", "overallassessment": "Overall Assessment", "recommendations": "Recommendations", "documentstructure": "Document Structure", "legalclausesanalysis": "Legal Clauses Analysis", "termsdefinitions": "Terms & Definitions", "complianceassessment": "Compliance Assessment", "riskassessment": "Risk Assessment", "legalrecommendations": "Legal Recommendations", "actionitems": "Action Items", "jurisdictionanalysis": "Jurisdiction Analysis", "bomcomponentsfound": "BOM Components Found", "sketchcomponentsfound": "Sketch Components Found", "clientcommunicationsfound": "Client Communications Found", "discrepanciesfound": "Discrepancies Found", "factoryreadinessassessment": "Factory Readiness Assessment", "keyfindingssummary": "Key Findings Summary", "productiontimeline": "Production Timeline", "contentaccuracyverification": "Content Accuracy Verification", "learningobjectivealignment": "Learning Objective Alignment", "assessmentcriteriachecking": "Assessment Criteria Checking", "educationalstandardcompliance": "Educational Standard Compliance", "studentprogressevaluation": "Student Progress Evaluation", "curriculumstructureanalysis": "Curriculum Structure Analysis", "pedagogicalapproachassessment": "Pedagogical Approach Assessment", "learningoutcomesmapping": "Learning Outcomes Mapping", "instructionaldesignreview": "Instructional Design Review"}}, "optionalSections": {"title": "Optional Sections", "sections": {"stakeholderanalysisimpact": "Stakeholder Analysis & Impact", "resourcerequirementsassessment": "Resource Requirements Assessment", "timelinemilestoneanalysis": "Timeline & Milestone Analysis", "communicationstrategy": "Communication Strategy", "successmetricskpis": "Success Metrics & KPIs", "legalprecedentanalysis": "Legal Precedent Analysis", "contractlifecyclemanagement": "Contract Lifecycle Management", "disputeresolutionmechanisms": "Dispute Resolution Mechanisms", "regulatoryupdateschanges": "Regulatory Updates & Changes", "stakeholderimpactassessment": "Stakeholder Impact Assessment", "costanalysisbudgetimpact": "Cost Analysis & Budget Impact", "supplierevaluationsourcing": "Supplier Evaluation & Sourcing", "qualitystandardscompliance": "Quality Standards Compliance", "sustainabilityenvironmentalimpact": "Sustainability & Environmental Impact", "riskmitigationstrategies": "Risk Mitigation Strategies", "advancedlearninganalytics": "Advanced Learning Analytics", "accessibilityinclusionassessment": "Accessibility & Inclusion Assessment", "technologyintegrationevaluation": "Technology Integration Evaluation", "parentteachercommunication": "Parent-Teacher Communication", "professionalDevelopmentneeds": "Professional Development Needs"}}}, "reportSettings": {"title": "3. Report Settings", "reportLanguage": {"label": "Report Language", "options": {"english": "English", "chinese": "Chinese (Traditional)", "chineseSimplified": "Chinese (Simplified)", "japanese": "Japanese", "korean": "Korean", "spanish": "Spanish", "french": "French", "german": "German", "italian": "Italian", "portuguese": "Portuguese"}}, "reportLength": {"label": "Report Length", "options": {"short": "Short (500 words) - Quick Summary", "medium": "Medium (1000 words) - Balanced Analysis", "long": "Long (2000 words) - Comprehensive Insights", "detailed": "Detailed (3000+ words) - In-depth Analysis"}}}, "uploadDocument": {"title": "4. Upload Document", "uploadArea": {"title": "Upload your document", "titleDrop": "Drop your file here", "description": "Drag and drop your file here, or click to browse", "descriptionDrop": "Release to upload your file", "supportedFormats": "Supported formats: .pdf, .doc, .docx, .pptx, .txt", "maxFileSize": "Maximum file size: 50MB"}, "startAnalysisButton": "Start Analysis (1 Credit)"}}, "actions": {"edit": "Edit", "delete": "Delete", "moveUp": "Move Up", "moveDown": "Move Down"}, "validation": {"selectAnalysisType": "Please select an analysis type", "addAnalysisSections": "Please add at least one analysis section", "selectReportLanguage": "Please select a report language", "selectReportLength": "Please select a report length", "uploadDocument": "Please upload a document"}, "loading": {"uploadingDocument": "Uploading document...", "analyzingDocument": "Analyzing document...", "generatingReport": "Generating report..."}}