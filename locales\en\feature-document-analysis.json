{"pageTitle": "Document Analysis", "pageDescription": "Choose your analysis type and configure sections to extract targeted insights from your documents.", "historyButton": "History", "sections": {"selectAnalysisType": {"title": "1. Select Analysis Type", "placeholder": "Choose how you want to analyze your document", "useCases": {"finance": {"name": "Finance Report Analysis", "description": "Analyze financial reports and assess business performance", "analysisFocus": ["Financial data extraction", "Trend analysis", "Key metric calculation", "Risk indicator identification", "Performance summarization"]}, "office": {"name": "Office Document Analysis", "description": "General analysis for office documents and reports", "analysisFocus": ["Content structure analysis", "Information completeness", "Consistency checking", "Quality assessment", "Actionable insights extraction"]}, "legal": {"name": "Legal Document Validation", "description": "Validate legal documents for compliance and consistency", "analysisFocus": ["Clause consistency analysis", "Legal compliance checking", "Term definition validation", "Cross-reference verification", "Risk assessment"]}, "garment-bom": {"name": "Garment BOM Factory Readiness", "description": "Analyze garment bills of materials and assess factory readiness", "analysisFocus": ["BOM component extraction", "Sketch annotation analysis", "Client communication detection", "Cross-reference discrepancy detection", "Factory readiness assessment"]}, "education": {"name": "Education Content Validation", "description": "Validate educational content and assess learning objectives", "analysisFocus": ["Content accuracy verification", "Learning objective alignment", "Assessment criteria checking", "Educational standard compliance", "Student progress evaluation"]}, "custom": {"name": "Custom Analysis", "description": "Create your own custom analysis template", "analysisFocus": ["Custom analysis focus 1", "Custom analysis focus 2", "Custom analysis focus 3"]}}, "moreText": "more"}, "configureAnalysisSections": {"title": "2. Configure Analysis Sections", "selectAnalysisType": "Select an analysis type", "chooseAnalysisType": "Choose an analysis type above to configure sections", "analysisSections": {"title": "Analysis Sections", "addButton": "Add", "placeholder": "Enter section name...", "sections": {"documentInformation": "Document Information", "contentOverview": "Content Overview", "structureAnalysis": "Structure Analysis", "keyInformationExtracted": "Key Information Extracted", "qualityAssessment": "Quality Assessment", "issuesFound": "Issues Found", "overallAssessment": "Overall Assessment", "recommendations": "Recommendations"}}, "optionalSections": {"title": "Optional Sections"}}, "reportSettings": {"title": "3. Report Settings", "reportLanguage": {"label": "Report Language", "options": {"english": "English", "chinese": "Chinese (Traditional)", "chineseSimplified": "Chinese (Simplified)", "japanese": "Japanese", "korean": "Korean", "spanish": "Spanish", "french": "French", "german": "German", "italian": "Italian", "portuguese": "Portuguese"}}, "reportLength": {"label": "Report Length", "options": {"short": "Short (500 words) - Quick Summary", "medium": "Medium (1000 words) - Balanced Analysis", "long": "Long (2000 words) - Comprehensive Insights", "detailed": "Detailed (3000+ words) - In-depth Analysis"}}}, "uploadDocument": {"title": "4. Upload Document", "uploadArea": {"title": "Upload your document", "titleDrop": "Drop your file here", "description": "Drag and drop your file here, or click to browse", "descriptionDrop": "Release to upload your file", "supportedFormats": "Supported formats: .pdf, .doc, .docx, .pptx, .txt", "maxFileSize": "Maximum file size: 50MB"}, "startAnalysisButton": "Start Analysis (1 Credit)"}}, "actions": {"edit": "Edit", "delete": "Delete", "moveUp": "Move Up", "moveDown": "Move Down"}, "validation": {"selectAnalysisType": "Please select an analysis type", "addAnalysisSections": "Please add at least one analysis section", "selectReportLanguage": "Please select a report language", "selectReportLength": "Please select a report length", "uploadDocument": "Please upload a document"}, "loading": {"uploadingDocument": "Uploading document...", "analyzingDocument": "Analyzing document...", "generatingReport": "Generating report..."}}