import { NextRequest, NextResponse } from 'next/server'
import { verifyOTP } from '@/lib/otp'
import { verifyUserEmail, getUserByEmail } from '@/lib/db'
import { generateToken } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')
    const email = searchParams.get('email')

    // Validate input
    if (!token || !email) {
      return NextResponse.json(
        { error: 'Token and email are required' },
        { status: 400 }
      )
    }

    // Get user by email
    const user = await getUserByEmail(email)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already verified
    if (user.emailVerified) {
      // User is already verified, generate sign-in token and return success
      const signInToken = generateToken(
        {
          userId: user.id,
          email: user.email,
          purpose: 'auto_signin_after_verification'
        },
        '5m' // Token expires in 5 minutes
      )

      // Check if this is a new user (just got welcome credits)
      const isNewUser = user.credits === 10

      return NextResponse.json(
        {
          message: 'Email already verified! Signing you in...',
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            credits: user.credits,
            emailVerified: user.emailVerified,
          },
          signInToken,
          isNewUser
        },
        { status: 200 }
      )
    }

    // Verify the email token (using OTP verification since the token is stored as OTP)
    const verification = await verifyOTP(user.id, token, 'email_verification')
    
    if (!verification.valid) {
      if (verification.expired) {
        return NextResponse.json(
          { 
            error: 'This verification link has expired. Please request a new one.',
            code: 'TOKEN_EXPIRED'
          },
          { status: 400 }
        )
      }
      
      return NextResponse.json(
        { 
          error: 'Invalid verification link. Please check your email and try again.',
          code: 'INVALID_TOKEN'
        },
        { status: 400 }
      )
    }

    // Token is valid, verify the user's email and give welcome credits
    const verifiedUser = await verifyUserEmail(user.id)

    console.log(`✅ Email verified via link for user: ${verifiedUser.email}`)

    // Generate a temporary sign-in token for automatic login
    const signInToken = generateToken(
      {
        userId: verifiedUser.id,
        email: verifiedUser.email,
        purpose: 'auto_signin_after_verification'
      },
      '5m' // Token expires in 5 minutes
    )

    // Check if this is a new user (just got welcome credits)
    const isNewUser = verifiedUser.credits === 10

    // Return success response with sign-in token (same format as OTP verification)
    return NextResponse.json(
      {
        message: 'Email verified successfully! Signing you in...',
        user: {
          id: verifiedUser.id,
          email: verifiedUser.email,
          name: verifiedUser.name,
          credits: verifiedUser.credits,
          emailVerified: verifiedUser.emailVerified,
        },
        signInToken,
        isNewUser
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Email verification error:', error)
    return NextResponse.json(
      { error: 'An error occurred during verification. Please try again.' },
      { status: 500 }
    )
  }
}
