'use client'

import { useState, useRef, useEffect } from 'react'
import { Volume2, VolumeX } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTranslation } from '@/hooks/use-translation'

interface IntroVideoSectionProps {
  className?: string
}

export function IntroVideoSection({ className = '' }: IntroVideoSectionProps) {
  const { t } = useTranslation('homepage')
  const [isMuted, setIsMuted] = useState(true)
  const [showControls, setShowControls] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      // Auto-play when component mounts and video is loaded
      const handleCanPlay = () => {
        setIsLoaded(true)
        video.play().catch(console.error)
      }

      video.addEventListener('canplay', handleCanPlay)

      return () => {
        video.removeEventListener('canplay', handleCanPlay)
      }
    }
  }, [])

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            {t('videoSection.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('videoSection.description')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div
            className="relative rounded-lg overflow-hidden shadow-2xl group"
            onMouseEnter={() => setShowControls(true)}
            onMouseLeave={() => setShowControls(false)}
          >
            <video
              ref={videoRef}
              className="w-full h-auto"
              autoPlay
              muted={isMuted}
              loop
              playsInline
              preload="auto"
            >
              <source src="/DocuChamp Intro.webm" type="video/webm" />
              Your browser does not support the video tag.
            </video>

            {/* Minimal Controls Overlay - Only shows on hover */}
            <div
              className={`absolute inset-0 transition-opacity duration-300 ${
                showControls ? 'opacity-100' : 'opacity-0'
              }`}
            >
              {/* Sound Control - Bottom Right */}
              <div className="absolute bottom-4 right-4">
                <Button
                  onClick={toggleMute}
                  size="sm"
                  variant="ghost"
                  className="bg-black/50 hover:bg-black/70 text-white rounded-full w-10 h-10 p-0 transition-all duration-200"
                >
                  {isMuted ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Loading State */}
            {!isLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="text-gray-600 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                  <p className="text-sm">{t('videoSection.loadingText')}</p>
                </div>
              </div>
            )}
          </div>

          {/* Video Description */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t('videoSection.videoDescription')}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
