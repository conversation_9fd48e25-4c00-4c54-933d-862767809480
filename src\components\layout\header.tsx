'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { AuthModal } from '@/components/auth/auth-modal'
import { showLowCreditsNotification } from '@/lib/notifications'

import { LogOut, Settings, CreditCard, LayoutDashboard, Coins, DollarSign } from 'lucide-react'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { useTranslation } from '@/hooks/use-translation'

export function Header() {
  const { data: session } = useSession()
  const { t } = useTranslation('common')

  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [credits, setCredits] = useState<number | null>(null)
  const [authModal, setAuthModal] = useState<{ isOpen: boolean; mode: 'signin' | 'signup' }>({
    isOpen: false,
    mode: 'signin'
  })


  // Fetch user credits
  const fetchCredits = () => {
    if (session) {
      fetch('/api/credits/balance')
        .then(res => res.json())
        .then(data => {
          const newBalance = data.balance
          setCredits(newBalance)

          // Show low credits notification if balance is low
          if (newBalance <= 5 && newBalance > 0) {
            showLowCreditsNotification(newBalance)
          }
        })
        .catch(err => console.error('Failed to fetch credits:', err))
    }
  }

  useEffect(() => {
    fetchCredits()
  }, [session])

  // Listen for credit balance changes
  useEffect(() => {
    const handleCreditChange = () => {
      fetchCredits()
    }

    window.addEventListener('creditBalanceChanged', handleCreditChange)
    return () => window.removeEventListener('creditBalanceChanged', handleCreditChange)
  }, [session])

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  return (
    <header className="bg-white shadow-sm border-b relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center" title="DocuChampAI - Home">
              <img
                src="/docuchampai.webp"
                alt="DocuChampAI"
                className="h-6 sm:h-8 w-auto object-contain"
              />
            </Link>
          </div>



          {/* User Menu */}
          <div className="flex items-center space-x-2">
            {/* Language Switcher */}
            <LanguageSwitcher variant="minimal" />

            {/* Subscribe Button - Always visible */}
            <Link href="/pricing" title="Subscribe to DocuChampAI">
              <Button
                variant="outline"
                className="hidden sm:inline-flex border-purple-200 text-purple-600 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-700 transition-all duration-200"
                size="sm"
              >
                {t('buttons.subscribe')}
              </Button>
            </Link>

            {session ? (
              <>
                {/* Credits Display - Mobile and Desktop */}
                <div className="flex items-center space-x-1 sm:space-x-2 bg-purple-50 px-2 sm:px-3 py-1 rounded-full relative group">
                  <Coins className="w-3 h-3 sm:w-4 sm:h-4 text-purple-500" />
                  <span className="text-xs sm:text-sm font-medium text-purple-700">
                    {credits !== null ? credits : '...'}
                  </span>
                  {/* Tooltip */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-50 shadow-lg">
                    Credits available: {credits !== null ? credits : '...'}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                  </div>
                </div>

                <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                >
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {session.user?.name?.charAt(0) || session.user?.email?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <span className="hidden md:block text-sm font-medium">
                    {session.user?.name || session.user?.email}
                  </span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <Link
                      href="/dashboard"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                      title="Go to Dashboard"
                    >
                      <LayoutDashboard className="w-4 h-4 mr-2" />
                      {t('header.userMenu.dashboard')}
                    </Link>
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                      title="Manage Profile Settings"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      {t('header.userMenu.profileSettings')}
                    </Link>
                    <Link
                      href="/billing"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                      title="Manage Billing and Subscriptions"
                    >
                      <CreditCard className="w-4 h-4 mr-2" />
                      {t('header.userMenu.billing')}
                    </Link>
                    <Link
                      href="/pricing"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                      title="View Pricing Plans"
                    >
                      <DollarSign className="w-4 h-4 mr-2" />
                      {t('header.userMenu.pricing')}
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      {t('navigation.signOut')}
                    </button>
                  </div>
                )}
              </div>
              </>
            ) : (
              <div className="flex items-center space-x-2 sm:space-x-4">
                {/* Mobile Subscribe Button */}
                <Link href="/pricing" title="Subscribe to DocuChampAI" className="sm:hidden">
                  <Button
                    variant="outline"
                    className="border-purple-200 text-purple-600 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-700 transition-all duration-200"
                    size="sm"
                  >
                    {t('buttons.subscribe')}
                  </Button>
                </Link>

                <Button
                  variant="ghost"
                  onClick={() => setAuthModal({ isOpen: true, mode: 'signin' })}
                  className="hidden sm:inline-flex"
                >
                  {t('navigation.signIn')}
                </Button>
                <Button
                  onClick={() => setAuthModal({ isOpen: true, mode: 'signup' })}
                  className="text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 !rounded-md hover:!rounded-md"
                  size="sm"
                >
                  {t('navigation.signUp')}
                </Button>
              </div>
            )}


          </div>
        </div>


      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={() => setAuthModal({ ...authModal, isOpen: false })}
        initialMode={authModal.mode}
      />
    </header>
  )
}
