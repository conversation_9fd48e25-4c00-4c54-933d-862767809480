'use client'

import { useState } from 'react'
import { Share2, Facebook, Linkedin, Link2, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SocialShareProps {
  url?: string
  title?: string
  description?: string
  className?: string
}

export function SocialShare({ 
  url = typeof window !== 'undefined' ? window.location.href : '',
  title = 'DocuChampAI - AI Document Analysis & Automation',
  description = 'Transform your document workflow with AI. Extract data from PDFs, analyze charts & tables, automate reports.',
  className = ''
}: SocialShareProps) {
  const [copied, setCopied] = useState(false)

  const encodedUrl = encodeURIComponent(url)
  const encodedTitle = encodeURIComponent(title)
  const encodedDescription = encodeURIComponent(description)

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    x: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const handleShare = (platform: keyof typeof shareLinks) => {
    window.open(shareLinks[platform], '_blank', 'width=600,height=400')
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-sm text-gray-600 mr-2">Share:</span>
      
      <button
        onClick={() => handleShare('facebook')}
        className="group relative overflow-hidden rounded-lg bg-blue-500 p-3 text-white shadow-md transition-all duration-300 hover:bg-blue-600 hover:shadow-lg hover:scale-105 active:scale-95"
        title="Share on Facebook"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <Facebook className="relative w-4 h-4" />
      </button>

      <button
        onClick={() => handleShare('x')}
        className="group relative overflow-hidden rounded-lg bg-gray-800 p-3 text-white shadow-md transition-all duration-300 hover:bg-gray-900 hover:shadow-lg hover:scale-105 active:scale-95"
        title="Share on X (formerly Twitter)"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <svg className="relative w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      </button>

      <button
        onClick={() => handleShare('linkedin')}
        className="group relative overflow-hidden rounded-lg bg-blue-600 p-3 text-white shadow-md transition-all duration-300 hover:bg-blue-700 hover:shadow-lg hover:scale-105 active:scale-95"
        title="Share on LinkedIn"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <Linkedin className="relative w-4 h-4" />
      </button>

      <button
        onClick={handleCopyLink}
        className="group relative overflow-hidden rounded-lg bg-gray-600 p-3 text-white shadow-md transition-all duration-300 hover:bg-gray-700 hover:shadow-lg hover:scale-105 active:scale-95"
        title="Copy link"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        {copied ? (
          <Check className="relative w-4 h-4 text-green-300" />
        ) : (
          <Link2 className="relative w-4 h-4" />
        )}
      </button>
    </div>
  )
}

// Floating social share button for mobile
export function FloatingSocialShare(props: SocialShareProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="fixed bottom-4 right-4 z-50 md:hidden">
      {isOpen && (
        <div className="mb-2 bg-white rounded-lg shadow-lg border p-3">
          <SocialShare {...props} className="flex-col space-x-0 space-y-2" />
        </div>
      )}
      
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="rounded-full w-12 h-12 bg-purple-600 hover:bg-purple-700 shadow-lg"
        title="Share this page"
      >
        <Share2 className="w-5 h-5 text-white" />
      </Button>
    </div>
  )
}
