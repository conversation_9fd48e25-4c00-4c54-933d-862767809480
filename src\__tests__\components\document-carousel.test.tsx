import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import DocumentCarouselSection from '@/components/home/<USER>'

// Mock the translation hook
jest.mock('@/hooks/use-translation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, any> = {
        'carousel.title': 'Your Everyday AI Document Assistant',
        'carousel.description': 'From busy workdays to important projects, our AI companion makes document analysis effortless.',
        'carousel.selectType': 'Document Examples:',
        'carousel.categories.contract': 'Contract Documents',
        'carousel.categories.financial': 'Financial Documents',
        'carousel.categories.generic': 'Business Documents',
        'carousel.categories.handwritten': 'Handwritten Documents',
        'carousel.categories.insurance': 'Insurance Documents',
        'carousel.categories.manufacturing': 'Manufacturing Specifications',
        'carousel.sampleCount.single': 'sample',
        'carousel.sampleCount.multiple': 'samples'
      }
      return translations[key] || key
    }
  })
}))

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />
  }
})

describe('DocumentCarouselSection', () => {
  it('renders the carousel with title and description', () => {
    render(<DocumentCarouselSection />)

    expect(screen.getByText('Turning Paperwork Into Peace of Mind')).toBeInTheDocument()
    expect(screen.getByText('Let our AI lighten your load—summarizing and extracting key points from insurance forms, receipts, contracts, and more, so you can breathe a little easier.')).toBeInTheDocument()
  })

  it('renders all document categories', () => {
    render(<DocumentCarouselSection />)

    expect(screen.getByText('Contract Documents')).toBeInTheDocument()
    expect(screen.getByText('Financial Documents')).toBeInTheDocument()
    expect(screen.getByText('Business Documents')).toBeInTheDocument()
    expect(screen.getByText('Handwritten Documents')).toBeInTheDocument()
    expect(screen.getByText('Insurance Documents')).toBeInTheDocument()
    expect(screen.getByText('Manufacturing Specifications')).toBeInTheDocument()
  })

  it('renders document buttons', () => {
    render(<DocumentCarouselSection />)

    expect(screen.getByText('NDA')).toBeInTheDocument()
    expect(screen.getByText('Financial Report')).toBeInTheDocument()
    expect(screen.getByText('Invoice')).toBeInTheDocument()
  })

  it('renders the carousel component', () => {
    render(<DocumentCarouselSection />)

    // The section should contain the carousel
    const section = screen.getByRole('region')
    expect(section).toBeInTheDocument()
  })
})
