/* Embla Carousel Styles */

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  display: flex;
  touch-action: pan-y;
}

.embla__slide {
  flex: 0 0 80%;
  min-width: 0;
  position: relative;
}

@media (min-width: 768px) {
  .embla__slide {
    flex: 0 0 55%;
  }
}

@media (min-width: 1024px) {
  .embla__slide {
    flex: 0 0 40%;
  }
}

@media (min-width: 1280px) {
  .embla__slide {
    flex: 0 0 35%;
  }
}

@media (min-width: 1536px) {
  .embla__slide {
    flex: 0 0 30%;
  }
}

.embla__button {
  z-index: 1;
  color: #333;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 4rem;
  height: 4rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.embla__button:hover {
  background-color: white;
  transform: scale(1.1);
}

.embla__button:disabled {
  opacity: 0.3;
  cursor: default;
}

.embla__button__svg {
  width: 35%;
  height: 35%;
}

.embla__dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
}

.embla__dot {
  width: 0.75rem;
  height: 0.75rem;
  margin: 0 0.5rem;
  border-radius: 50%;
  background-color: #ccc;
  transition: all 0.2s ease-in-out;
}

.embla__dot--selected {
  background: linear-gradient(to right, #8b5cf6, #3b82f6, #ec4899);
  transform: scale(1.2);
}
