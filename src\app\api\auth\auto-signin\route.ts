import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth-utils'
import { getUserById } from '@/lib/db'
import { signIn } from 'next-auth/react'

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()

    // Validate input
    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      )
    }

    // Verify the token
    const payload = verifyToken(token)
    if (!payload || payload.purpose !== 'auto_signin_after_verification') {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 400 }
      )
    }

    // Get user details
    const user = await getUserById(payload.userId)
    if (!user || !user.emailVerified) {
      return NextResponse.json(
        { error: 'User not found or email not verified' },
        { status: 400 }
      )
    }

    // Return user credentials for NextAuth sign-in
    return NextResponse.json(
      { 
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        }
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Auto sign-in error:', error)
    return NextResponse.json(
      { error: 'An error occurred during automatic sign-in. Please try signing in manually.' },
      { status: 500 }
    )
  }
}
