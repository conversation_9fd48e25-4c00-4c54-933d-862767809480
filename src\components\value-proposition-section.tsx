'use client'

import { Check, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTranslation } from '@/hooks/use-translation'
import { useRouter } from 'next/navigation'

export function ValuePropositionSection() {
  const { t: tPricing } = useTranslation('pricing')
  const { t: tHomepage } = useTranslation('homepage')
  const router = useRouter()

  // Helper function to safely get array from translation
  const getTranslationArray = (key: string, fallback: string[] = []): string[] => {
    try {
      const value = tPricing(key) as any
      return Array.isArray(value) ? value : fallback
    } catch (error) {
      console.warn(`Translation key "${key}" not found, using fallback`)
      return fallback
    }
  }

  const struggles = getTranslationArray('struggles', [
    "Massive document handling every day?",
    "Eye-ball checking repetitive content?",
    "Manual data extraction taking hours?",
    "Repetitive copy-paste tasks?"
  ])

  const professionals = getTranslationArray('professionals', [
    "Legal: Compliance & Proofreading",
    "Finance: Financial Document Processing",
    "Construction: RFP & Proposal Review",
    "Clerk: Data Entry & Document Handling",
    "Healthcare: Medical Records, Diagnostic Reports",
    "Education: Course Plans, Student Reports",
    "Human Resources: Job Postings, Employee Contracts",
    "Sales: Contract Documents, Quotations"
  ])

  const handleGetStarted = () => {
    router.push('/pricing')
  }

  return (
    <section className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


        {/* Modern Value Proposition Section */}
        <div className="mb-16">
          {/* Problems Section */}
          <div className="mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">{tPricing('strugglesTitle')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {struggles.map((struggle, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-100">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <X className="w-4 h-4 text-red-600" />
                  </div>
                  <span className="text-gray-700 leading-relaxed">{struggle}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Solutions Section */}
          <div>
            <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">{tPricing('professionalsTitle')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
              {professionals.map((professional, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-gray-700 leading-relaxed text-sm">{professional}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="text-center mb-8">
          <p className="text-lg text-gray-700 mb-6">
            {tHomepage('valueProposition.heavyLifting')}
          </p>
          <Button
            onClick={handleGetStarted}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3 text-lg"
          >
            {tHomepage('valueProposition.startSaving')}
          </Button>
        </div>
      </div>
    </section>
  )
}
