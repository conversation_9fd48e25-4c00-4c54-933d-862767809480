'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/use-translation'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  FileText,
  Menu
} from 'lucide-react'
import { getCookie, setCookie } from '@/lib/utils'

interface SidebarProps {
  className?: string
}

const getNavigationItems = (t: any) => [
  {
    name: t('navigation.documentAnalysis.name'),
    href: '/features/document-analysis',
    icon: FileText,
    description: t('navigation.documentAnalysis.description')
  }
]

export function Sidebar({ className = '' }: SidebarProps) {
  const { t } = useTranslation('common')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const pathname = usePathname()
  const navigationItems = getNavigationItems(t)

  // Load sidebar state from cookie on mount
  useEffect(() => {
    const savedState = getCookie('sidebar-collapsed')
    if (savedState !== null) {
      setIsCollapsed(savedState === 'true')
    }
  }, [])

  // Save sidebar state to cookie when it changes
  const toggleCollapsed = () => {
    const newState = !isCollapsed
    setIsCollapsed(newState)
    setCookie('sidebar-collapsed', newState.toString())
  }

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={() => setIsMobileOpen(!isMobileOpen)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-md shadow-lg border border-gray-200"
      >
        <Menu className="w-5 h-5 text-gray-600" />
      </button>

      {/* Mobile overlay - Fixed to prevent zoom blackout */}
      {isMobileOpen && (
        <div
          className="lg:hidden mobile-overlay"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          ${isCollapsed ? 'w-16' : 'w-56 xl:w-64'}
          ${isMobileOpen ? 'fixed top-16 bottom-0 left-0 z-40 w-64 sm:w-56' : 'hidden lg:block'}
          bg-white border-r border-gray-200 transition-all duration-300 ease-in-out flex-shrink-0
          ${className}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-2">
            <div className="flex justify-between items-center">
              <button
                onClick={toggleCollapsed}
                className="hidden lg:flex items-center justify-center w-12 h-12 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <Menu className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 pb-4 space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <div key={item.name} className="flex justify-center relative group">
                  <Link
                    href={item.href}
                    className={`
                      flex items-center w-12 h-12 rounded-lg transition-colors duration-200
                      ${isActive
                        ? 'bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 border border-purple-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }
                      ${(!isCollapsed || isMobileOpen) ? 'w-full justify-start px-3' : 'justify-center'}
                    `}
                    onClick={() => setIsMobileOpen(false)}
                    title={item.description}
                  >
                    <div className="flex items-center justify-center w-5 h-5 flex-shrink-0">
                      <Icon className="w-5 h-5" />
                    </div>
                    {(!isCollapsed || isMobileOpen) && (
                      <div className="flex-1 min-w-0 ml-3">
                        <div className="font-medium truncate">{item.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {item.description}
                        </div>
                      </div>
                    )}
                  </Link>

                  {/* Tooltip for collapsed state - only show in desktop mode */}
                  {isCollapsed && !isMobileOpen && (
                    <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-50 shadow-lg">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-gray-300 mt-1">{item.description}</div>
                      {/* Arrow pointing to the icon */}
                      <div className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900"></div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>


        </div>
      </div>
    </>
  )
}
