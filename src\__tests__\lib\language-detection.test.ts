/**
 * @jest-environment jsdom
 */

import {
  detectUserLanguage,
  detectBrowserLanguage,
  saveLanguagePreference,
  getSavedLanguagePreference,
  clearLanguagePreference,
  isFirstVisit,
  markLanguageDetected,
  getLanguageDisplayName,
  getLanguageFlag,
  detectUserRegion
} from '@/lib/language-detection'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock navigator
const navigatorMock = {
  language: 'en-US',
  languages: ['en-US', 'en']
}

Object.defineProperty(window, 'navigator', {
  value: navigatorMock,
  writable: true
})

describe('Language Detection', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('detectUserLanguage', () => {
    it('should return saved preference when available', () => {
      localStorageMock.getItem.mockReturnValue('zh')
      
      const result = detectUserLanguage()
      expect(result).toBe('zh')
      expect(localStorageMock.getItem).toHaveBeenCalledWith('docuchamp-language-preference')
    })

    it('should detect browser language when no saved preference', () => {
      localStorageMock.getItem.mockReturnValue(null)
      navigatorMock.language = 'zh-TW'
      
      const result = detectUserLanguage()
      expect(result).toBe('zh')
    })

    it('should fallback to English when no detection possible', () => {
      localStorageMock.getItem.mockReturnValue(null)
      navigatorMock.language = 'fr-FR'
      
      const result = detectUserLanguage()
      expect(result).toBe('en')
    })

    it('should ignore invalid saved preferences', () => {
      localStorageMock.getItem.mockReturnValue('invalid-locale')
      navigatorMock.language = 'en-US'
      
      const result = detectUserLanguage()
      expect(result).toBe('en')
    })
  })

  describe('detectBrowserLanguage', () => {
    it('should detect English variants', () => {
      navigatorMock.language = 'en-US'
      navigatorMock.languages = ['en-US', 'en-GB']
      
      const result = detectBrowserLanguage()
      expect(result).toBe('en')
    })

    it('should detect Traditional Chinese variants', () => {
      const testCases = [
        'zh-TW',
        'zh-HK',
        'zh-MO',
        'zh-Hant'
      ]

      testCases.forEach(lang => {
        navigatorMock.language = lang
        const result = detectBrowserLanguage()
        expect(result).toBe('zh')
      })
    })

    it('should detect Simplified Chinese variants', () => {
      const testCases = [
        'zh-CN',
        'zh-SG',
        'zh-Hans'
      ]

      testCases.forEach(lang => {
        navigatorMock.language = lang
        const result = detectBrowserLanguage()
        expect(result).toBe('zh')
      })
    })

    it('should return null for unsupported languages', () => {
      navigatorMock.language = 'fr-FR'
      navigatorMock.languages = ['fr-FR', 'de-DE']
      
      const result = detectBrowserLanguage()
      expect(result).toBe(null)
    })

    it('should check multiple browser languages', () => {
      navigatorMock.language = 'fr-FR'
      navigatorMock.languages = ['fr-FR', 'zh-TW', 'en-US']
      
      const result = detectBrowserLanguage()
      expect(result).toBe('zh')
    })
  })

  describe('Language preference storage', () => {
    it('should save language preference', () => {
      saveLanguagePreference('zh')
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'docuchamp-language-preference',
        'zh'
      )
    })

    it('should get saved language preference', () => {
      localStorageMock.getItem.mockReturnValue('zh')
      
      const result = getSavedLanguagePreference()
      expect(result).toBe('zh')
      expect(localStorageMock.getItem).toHaveBeenCalledWith('docuchamp-language-preference')
    })

    it('should return null for invalid saved preference', () => {
      localStorageMock.getItem.mockReturnValue('invalid')
      
      const result = getSavedLanguagePreference()
      expect(result).toBe(null)
    })

    it('should clear language preference', () => {
      clearLanguagePreference()
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('docuchamp-language-preference')
    })
  })

  describe('First visit detection', () => {
    it('should detect first visit', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const result = isFirstVisit()
      expect(result).toBe(true)
    })

    it('should detect returning visit', () => {
      localStorageMock.getItem.mockReturnValue('true')
      
      const result = isFirstVisit()
      expect(result).toBe(false)
    })

    it('should mark language as detected', () => {
      markLanguageDetected()
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'docuchamp-language-detected',
        'true'
      )
    })
  })

  describe('Language display utilities', () => {
    it('should return correct display names', () => {
      expect(getLanguageDisplayName('en')).toBe('English')
      expect(getLanguageDisplayName('zh')).toBe('繁體中文')
      expect(getLanguageDisplayName('en', 'zh')).toBe('English')
      expect(getLanguageDisplayName('zh', 'en')).toBe('繁體中文')
    })

    it('should return correct flags', () => {
      expect(getLanguageFlag('en')).toBe('🇺🇸')
      expect(getLanguageFlag('zh')).toBe('🇭🇰')
    })

    it('should fallback for unknown locales', () => {
      expect(getLanguageDisplayName('unknown' as any)).toBe('unknown')
      expect(getLanguageFlag('unknown' as any)).toBe('🌐')
    })
  })

  describe('Region detection', () => {
    it('should detect Hong Kong region', () => {
      // Mock Intl.DateTimeFormat
      const mockDateTimeFormat = jest.fn().mockReturnValue({
        resolvedOptions: () => ({ timeZone: 'Asia/Hong_Kong' })
      })
      
      global.Intl = { DateTimeFormat: mockDateTimeFormat } as any
      
      const result = detectUserRegion()
      expect(result).toBe('zh-region')
    })

    it('should detect Taiwan region', () => {
      const mockDateTimeFormat = jest.fn().mockReturnValue({
        resolvedOptions: () => ({ timeZone: 'Asia/Taipei' })
      })
      
      global.Intl = { DateTimeFormat: mockDateTimeFormat } as any
      
      const result = detectUserRegion()
      expect(result).toBe('zh-region')
    })

    it('should detect other regions', () => {
      const mockDateTimeFormat = jest.fn().mockReturnValue({
        resolvedOptions: () => ({ timeZone: 'America/New_York' })
      })
      
      global.Intl = { DateTimeFormat: mockDateTimeFormat } as any
      
      const result = detectUserRegion()
      expect(result).toBe('other')
    })

    it('should handle detection errors', () => {
      const mockDateTimeFormat = jest.fn().mockImplementation(() => {
        throw new Error('Detection failed')
      })
      
      global.Intl = { DateTimeFormat: mockDateTimeFormat } as any
      
      const result = detectUserRegion()
      expect(result).toBe(null)
    })
  })
})
