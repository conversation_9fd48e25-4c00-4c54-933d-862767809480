-- Migration to update usage_records table for document analysis operations
-- This migration transforms the existing usage tracking to support individual operations

-- First, backup existing data if needed
-- CREATE TABLE usage_records_backup AS SELECT * FROM usage_records;

-- Drop the existing unique constraint
ALTER TABLE `usage_records` DROP INDEX `userId_date`;

-- Add new columns
ALTER TABLE `usage_records` 
ADD COLUMN `operationId` VARCHAR(191) NULL,
ADD COLUMN `operationType` VARCHAR(191) NOT NULL DEFAULT 'document_analysis',
ADD COLUMN `creditSpent` INTEGER NOT NULL DEFAULT 1,
ADD COLUMN `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
ADD COLUMN `fileLink` VARCHAR(191) NULL,
ADD COLUMN `fileName` VARCHAR(191) NULL,
ADD COLUMN `fileSize` INTEGER NULL,
ADD COLUMN `metadata` JSON NULL,
ADD COLUMN `completedAt` DATETIME(3) NULL;

-- Remove old columns that are no longer needed
ALTER TABLE `usage_records` DROP COLUMN `count`;
ALTER TABLE `usage_records` DROP COLUMN `date`;

-- Add new indexes
CREATE UNIQUE INDEX `usage_records_operationId_key` ON `usage_records`(`operationId`);
CREATE INDEX `usage_records_userId_idx` ON `usage_records`(`userId`);
CREATE INDEX `usage_records_operationId_idx` ON `usage_records`(`operationId`);
CREATE INDEX `usage_records_status_idx` ON `usage_records`(`status`);
