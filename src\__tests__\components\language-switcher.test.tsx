/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { LanguageSwitcher } from '@/components/ui/language-switcher'

// Mock the useRouter hook
const mockPush = jest.fn()
jest.mock('next/router', () => ({
  useRouter: () => ({
    locale: 'en',
    locales: ['en', 'zh'],
    defaultLocale: 'en',
    push: mockPush,
    pathname: '/',
    query: {},
    asPath: '/'
  })
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('LanguageSwitcher', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('Dropdown variant (default)', () => {
    it('should render with current locale', () => {
      render(<LanguageSwitcher />)
      
      // Should show current language (English)
      expect(screen.getByText('English')).toBeInTheDocument()
      expect(screen.getByText('🇺🇸')).toBeInTheDocument()
    })

    it('should show dropdown when clicked', () => {
      render(<LanguageSwitcher />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      // Should show both language options
      expect(screen.getAllByText('English')).toHaveLength(2) // One in button, one in dropdown
      expect(screen.getByText('繁體中文')).toBeInTheDocument()
    })

    it('should save preference and navigate when language is changed', async () => {
      render(<LanguageSwitcher />)
      
      // Open dropdown
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      // Click on Chinese option
      const chineseOption = screen.getByText('繁體中文')
      fireEvent.click(chineseOption)
      
      // Should save preference
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'docuchamp-language-preference',
        'zh'
      )
      
      // Should navigate to Chinese version
      expect(mockPush).toHaveBeenCalledWith(
        { pathname: '/', query: {} },
        '/',
        { locale: 'zh' }
      )
    })
  })

  describe('Button variant', () => {
    it('should render as buttons', () => {
      render(<LanguageSwitcher variant="button" />)
      
      const buttons = screen.getAllByRole('button')
      expect(buttons).toHaveLength(2) // One for each language
      
      expect(screen.getByText('English')).toBeInTheDocument()
      expect(screen.getByText('繁體中文')).toBeInTheDocument()
    })

    it('should highlight current language', () => {
      render(<LanguageSwitcher variant="button" />)
      
      const buttons = screen.getAllByRole('button')
      // The English button should have different styling (current locale)
      // This would need more specific testing based on actual CSS classes
      expect(buttons[0]).toBeInTheDocument()
      expect(buttons[1]).toBeInTheDocument()
    })
  })

  describe('Minimal variant', () => {
    it('should render with globe icon only', () => {
      render(<LanguageSwitcher variant="minimal" showText={false} />)
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      
      // Should not show text by default in minimal mode
      expect(screen.queryByText('English')).not.toBeInTheDocument()
    })

    it('should show dropdown when clicked', () => {
      render(<LanguageSwitcher variant="minimal" />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      // Should show language options in dropdown
      expect(screen.getByText('English')).toBeInTheDocument()
      expect(screen.getByText('繁體中文')).toBeInTheDocument()
    })
  })

  describe('Props configuration', () => {
    it('should hide flags when showFlag is false', () => {
      render(<LanguageSwitcher showFlag={false} />)
      
      expect(screen.queryByText('🇺🇸')).not.toBeInTheDocument()
      expect(screen.queryByText('🇭🇰')).not.toBeInTheDocument()
    })

    it('should hide text when showText is false', () => {
      render(<LanguageSwitcher showText={false} />)
      
      // Should still show flag but not text
      expect(screen.getByText('🇺🇸')).toBeInTheDocument()
      expect(screen.queryByText('English')).not.toBeInTheDocument()
    })

    it('should apply custom className', () => {
      render(<LanguageSwitcher className="custom-class" />)
      
      const container = screen.getByRole('button').parentElement
      expect(container).toHaveClass('custom-class')
    })
  })

  describe('Accessibility', () => {
    it('should have proper aria-label', () => {
      render(<LanguageSwitcher />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', 'Change language')
    })

    it('should be keyboard accessible', () => {
      render(<LanguageSwitcher />)
      
      const button = screen.getByRole('button')
      
      // Should be focusable
      button.focus()
      expect(button).toHaveFocus()
      
      // Should open on Enter key
      fireEvent.keyDown(button, { key: 'Enter' })
      expect(screen.getByText('繁體中文')).toBeInTheDocument()
    })
  })

  describe('Error handling', () => {
    it('should handle navigation errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      mockPush.mockRejectedValueOnce(new Error('Navigation failed'))
      
      render(<LanguageSwitcher />)
      
      // Open dropdown and click Chinese
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      const chineseOption = screen.getByText('繁體中文')
      fireEvent.click(chineseOption)
      
      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to change language:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })
})
